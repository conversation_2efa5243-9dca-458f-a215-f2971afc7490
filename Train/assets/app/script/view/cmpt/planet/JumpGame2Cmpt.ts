import { HeroAction, HeroAnimation, PassengerLifeAnimation, PlanetEvent } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import { animHelper } from "../../../common/helper/AnimHelper";
import { gameHelper } from "../../../common/helper/GameHelper";
import { resHelper } from "../../../common/helper/ResHelper";
import ActionTree, { ActionNode } from "../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../model/planet/PlanetEmptyNode";
import PlanetWindCtrl from "../../planet/PlanetWindCtrl";
import HeroCmpt from "../hero/HeroCmpt";
import PlanetNodeCmpt from "./PlanetNodeCmpt";
import PlanetNodeRewardCmpt from "./PlanetNodeRewardCmpt";
import PlanetNodeRewardGroupCmpt from "./PlanetNodeRewardGroupCmpt";

const { ccclass, property } = cc._decorator;


const MAX_TIME = 2
const MAX_DIS = 1000
const MOVE_OFFSET = 200
const MAIN: string[] = [PlanetEvent.JUMP_1, PlanetEvent.JUMP_2, PlanetEvent.JUMP_1_1, PlanetEvent.JUMP_1_2]

// 移动停留点的移动类型
enum PointMoveType {
    VERTICAL = "vertical",
    HORIZONTAL = "horizontal"
}

// 移动停留点的状态数据
interface PointMoveData {
    originalX: number      // 原始X位置
    originalY: number      // 原始Y位置
    currentTime: number    // 当前动画时间
    moveType: PointMoveType // 移动类型
    cycleDuration: number  // 移动一个周期的时长（秒）
}

@ccclass
export default class JumpGame2Cmpt extends PlanetNodeCmpt {

    /* 用于地图裁剪 */
    @property(cc.Node)
    protected cropStartNode: cc.Node = null
    @property(cc.Node)
    protected cropEndNode: cc.Node = null

    public model: PlanetEmptyNode = null

    private gravity: number = 2000 //重力
    private minY: number = -500 //最小Y 标志着死亡

    private speed: cc.Vec2 = cc.v2()
    private isJump: boolean = false //是否在跳跃中
    private isControl: boolean = false //是否可操作
    private powerSp: cc.Sprite = null    // 跳跃力度

    private touchTime: number = 0

    private points: cc.Node[] = []
    private heroNode: cc.Node = null
    private lineIndex: number = 0
    private prePos: cc.Vec2 = cc.v2()
    private nextPos: cc.Vec2 = cc.v2()
    private intersectPos: cc.Vec2 = cc.v2()
    private reachLines: { start: cc.Vec2, end: cc.Vec2 }[][] = null
    private staticReachLines: { start: cc.Vec2, end: cc.Vec2 }[][] = null
    private movingPoints: Set<number> = new Set()
    // hero相对于移动停留点原始Y的偏移
    private heroYOffsetOnMovingPoint: number = 0
    // hero相对于移动停留点原始X的偏移
    private heroXOffsetOnMovingPoint: number = 0

    private actionTree: ActionTree = null
    private planetCtrl: PlanetWindCtrl = null

    private rewardNode: cc.Node

    private get curIndex() {
        return this.model.progress
    }
    private set curIndex(val) {
        this.model.progress = val
    }



    public isPointMoving(index: number): boolean {
        return this.movingPoints.has(index)
    }

    private initMovingPoints() {
        this.points.forEach((point, index) => {
            let moveType: PointMoveType | null = null

            if (point.name.endsWith("_move_v")) {
                moveType = PointMoveType.VERTICAL
            } else if (point.name.endsWith("_move_h")) {
                moveType = PointMoveType.HORIZONTAL
            } else if (point.name.endsWith("_move")) {
                // 兼容旧 上下移动
                moveType = PointMoveType.VERTICAL
            }

            if (moveType) {
                this.movingPoints.add(index)
                this.initPointMoveData(point, moveType)
            }
        })
    }

    public listenEventMaps() {
        return [
            { [EventType.PLAENT_CONTROL_TOUCH_START]: this.onTouchStart },
            { [EventType.PLAENT_CONTROL_TOUCH_END]: this.onTouchEnd },
            { [EventType.PLAENT_CONTROL_TOUCH_CANCEL]: this.onTouchEnd },
            { [EventType.PLAENT_CHANGE_JUMP_END]: this.onChange },
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public async init(model: PlanetEmptyNode, planetCtrl: PlanetWindCtrl) {
        super.init(model)
        this.planetCtrl = planetCtrl
        this.heroNode = planetCtrl.getHeroNode()

        this.powerSp = this.heroNode.Child('ui/power/bar', cc.Sprite)
        this.points = this.node.Child('points').children

        for (let i = this.curIndex + 1; i < this.points.length; i++) {
            const it = this.points[i]
            // end 一直显示 针对缠绕星
            if (it.name == "end") continue
            it.active = false
        }
        this.initMovingPoints()
        this.actionTree = new ActionTree().init(this)

        //摄像机用
        this.model.reachOffset = ut.convertToNodeAR(this.points[0], this.node)
        this.model.endOffset = ut.convertToNodeAR(this.points.last(), this.node)

        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
        this.node.zIndex = 0

        this.initRewards()
        this.sendCrop()
    }

    private sendCrop() {
        if (!this.cropStartNode || !this.cropEndNode) return
        const mapNode = this.planetCtrl.getMapNode()
        const start = ut.convertToNodeAR(this.cropStartNode, mapNode)
        const end = ut.convertToNodeAR(this.cropEndNode, mapNode)
        eventCenter.emit(EventType.PLANET_CROP_ACTIVE, { uuid: this.node.uuid, start, end })
    }

    private initRewards() {
        let rewardNode = this.Child("reward")
        this.rewardNode = rewardNode
        if (!rewardNode) return
        this.points.forEach((point, i) => {
            if (!this.model.checkRewardByPoint(i)) return
            if (point.Child(rewardNode.name)) return
            let node = cc.instantiate2(rewardNode, point)
            node.active = true
            node.setPosition(cc.v2(0, point.height / 2))
        })
    }

    private initLines() {
        if (this.staticReachLines) return
        // 只缓存静态停留点的线段
        this.staticReachLines = this.points.map((p, index) => {
            if (this.movingPoints.has(index)) {
                return null // 移动停留点不缓存
            }
            let lineNodes = p.children.filter(c => c.name.includes("line"))
            if (lineNodes.length <= 0 && index == this.points.length - 1) { //最后一个特殊处理
                return [{
                    start: ut.convertToNodeAR(p, this.node.parent, cc.v2(0, 0)),
                    end: ut.convertToNodeAR(p, this.node.parent, cc.v2(10000, 0)),
                }]
            }
            let lines = lineNodes.map(node => {
                let start = ut.convertToNodeAR(node, this.node.parent, cc.v2(-node.anchorX * node.width, 0))
                let end = ut.convertToNodeAR(node, this.node.parent, cc.v2((1 - node.anchorX) * node.width, 0))
                return { start, end }
            })
            return lines
        })

        // 保持向后兼容，reachLines指向staticReachLines
        this.reachLines = this.staticReachLines
    }

    // 实时计算移动停留点的碰撞线段
    private getMovingPointLines(index: number) {
        let point = this.points[index]
        let lineNodes = point.children.filter(c => c.name.includes("line"))

        let lines: { start: cc.Vec2, end: cc.Vec2 }[]

        if (lineNodes.length <= 0 && index == this.points.length - 1) {
            lines = [{
                start: ut.convertToNodeAR(point, this.node.parent, cc.v2(0, 0)),
                end: ut.convertToNodeAR(point, this.node.parent, cc.v2(10000, 0)),
            }]
        } else {
            lines = lineNodes.map(node => {
                let start = ut.convertToNodeAR(node, this.node.parent, cc.v2(-node.anchorX * node.width, 0))
                let end = ut.convertToNodeAR(node, this.node.parent, cc.v2((1 - node.anchorX) * node.width, 0))
                return { start, end }
            })
        }

        return lines
    }

    // 获取停留点的实时碰撞线段
    private getPointLines(index: number) {
        if (this.movingPoints.has(index)) {
            return this.getMovingPointLines(index) // 实时计算
        } else {
            return this.staticReachLines[index] // 使用缓存
        }
    }

    private async onTarget(model) {
        if (this.model != model) return
        let reachNode = this.points[0]
        if (!reachNode) return
        this.planetCtrl.focusHero()
        let hero = gameHelper.hero
        let pos = this.getFudaoCenter(this.curIndex)
        if (this.curIndex == 0) {
            await this.actionTree.start(async (action: ActionNode) => {
                await action.run(hero.moveToPos, pos, hero)
                action.ok()
            })
        } else {
            hero.setPosition(pos)
            // 如果当前停留点是移动的，计算偏移量
            if (this.isPointMoving(this.curIndex)) {
                const movingPoint = this.points[this.curIndex]
                const moveData: PointMoveData = movingPoint.Data

                if (moveData.moveType === PointMoveType.VERTICAL) {
                    this.heroYOffsetOnMovingPoint = pos.y - movingPoint.y
                    this.heroXOffsetOnMovingPoint = 0
                } else if (moveData.moveType === PointMoveType.HORIZONTAL) {
                    this.heroXOffsetOnMovingPoint = pos.x - movingPoint.x
                    this.heroYOffsetOnMovingPoint = 0
                }
            }
        }
        this.planetCtrl.focusHero({ back: true })
        hero.setAction(HeroAction.IDLE)
        await this.showPoint(this.curIndex + 1)
        this.setControl(true)
        hero.setAction(HeroAction.JUMP_GAME)
    }

    private getFudaoCenter(index) {
        let node = this.points[index]
        let line = node.children.find(c => c.name.includes("line"))
        let startPos = this.node.getPosition()
        let center = ut.convertToNodeAR(line, this.node)
        return cc.v2(startPos.x + center.x, startPos.y + center.y)
    }

    private onTouchStart(event: cc.Event.EventTouch) {
        if (!this.isControl) return
        this.touchTime = Date.now()
        this.heroNode.Component(HeroCmpt).playAnimation(HeroAnimation.JUMP2, true)
        this.showPower(true)
        gameHelper.hero.setJump()
    }

    private onTouchEnd(event: cc.Event.EventTouch) {
        if (!this.isControl || this.touchTime <= 0) {
            return
        }
        this.prePos = this.heroNode.getPosition(this.prePos)

        // 按住多少秒
        let time = (Date.now() - this.touchTime) * 0.001
        time = cc.misc.clampf(time, 0, MAX_TIME)

        let dis = time / MAX_TIME * MAX_DIS
        dis = this.clampDis(dis)
        this.setSpeedByDis(dis)

        // 检查两种Y位置计算方式的差异
        if (this.isPointMoving(this.curIndex)) {
            const currentPoint = this.points[this.curIndex];
            const heroFollowY = currentPoint.y + this.heroYOffsetOnMovingPoint;

            const lines = this.getPointLines(this.curIndex);
            const lineY = lines && lines.length > 0 ? lines[0].start.y : 0;
        }

        this.isJump = true
        this.setControl(false)

        // hero开始跳跃时，清除移动停留点偏移记录
        this.heroYOffsetOnMovingPoint = 0
        this.heroXOffsetOnMovingPoint = 0

        this.touchTime = 0

        this.showPower(false)
        this.heroNode.Component(HeroCmpt).playAnimation(HeroAnimation.JUMP3)
        this.checkReachEnd(dis, this.prePos.x)
    }

    private setSpeedByDis(dis: number) {
        // 1. 获取平台真实的瞬时速度
        const platformVelocity = this.getPlatformInstantaneousVelocity(this.curIndex);
        const platformVy = platformVelocity.y;

        // 2. 计算出玩家自身的、不受平台影响的“意图跳跃速度”
        const tan60 = ut.tan(60);
        const g = this.gravity;
        // 安全校验，避免dis为0或负数导致Math.sqrt(NaN)
        const vx_baseline = dis > 0 ? Math.sqrt(g * dis / (2 * tan60)) : 0;
        const playerJumpVy = vx_baseline * tan60; // 这是玩家“想跳”的高度

        let final_vy: number;

        // console.log("=== 跳跃计算调试 ===");
        // console.log(`📏 输入距离 dis: ${dis.toFixed(2)}`);
        // console.log(`📊 基准水平速度 vx_baseline: ${vx_baseline.toFixed(2)}`);
        // console.log(`📊 玩家意图垂直速度 playerJumpVy: ${playerJumpVy.toFixed(2)}`);
        // console.log(`🌊 平台瞬时速度 platformVy: ${platformVy.toFixed(2)}`);
        // console.log(`⚖️ 重力加速度 g: ${g}`);

        // 3. 应用垂直方向"柔性助推"逻辑
        if (platformVy > 0) {
            // 平台正在上升
            if (playerJumpVy < platformVy) {
                // 情况A: 玩家小跳，跟不上平台 -> 平台"托"一把
                final_vy = platformVy + playerJumpVy
                // console.log(`🆙 垂直逻辑: 平台托举模式（平台${platformVy.toFixed(2)} + 玩家${playerJumpVy.toFixed(2)}）`);
            } else {
                // 情况B: 玩家大跳，比平台快 -> 忽略平台速度，保证手感
                final_vy = playerJumpVy;
                // console.log(`🎮 垂直逻辑: 玩家主导模式（玩家意图 ${playerJumpVy.toFixed(2)} >= 平台速度 ${platformVy.toFixed(2)}）`);
            }
        } else {
            // 平台静止或正在下落，当作平地处理，保证跳跃手感稳定
            final_vy = playerJumpVy;
            // console.log(`🏔️ 垂直逻辑: 平地模式（平台静止或下降，忽略平台速度 ${platformVy.toFixed(2)}）`);
        }

        // 4. 应用水平方向"柔性助推"逻辑
        const platformVx = platformVelocity.x;
        let final_vx: number;

        if (platformVx > 0) {
            // 平台向右移动
            if (vx_baseline < platformVx) {
                // 玩家水平速度跟不上平台 -> 平台"托"一把
                final_vx = platformVx + vx_baseline;
                // console.log(`➡️ 水平逻辑: 平台托举模式（平台${platformVx.toFixed(2)} + 玩家${vx_baseline.toFixed(2)}）`);
            } else {
                // 玩家水平速度够快 -> 按平地处理
                final_vx = vx_baseline;
                // console.log(`🎮 水平逻辑: 玩家主导模式（玩家意图 ${vx_baseline.toFixed(2)} >= 平台速度 ${platformVx.toFixed(2)}）`);
            }
        } else {
            // 平台静止、向左，当作平地处理
            final_vx = vx_baseline;
            // console.log(`🏔️ 水平逻辑: 平地模式（平台速度 ${platformVx.toFixed(2)}）`);
        }

        // console.log(`🎯 最终垂直速度: ${final_vy.toFixed(2)}`);
        // console.log(`🎯 最终水平速度: ${final_vx.toFixed(2)}`);

        this.speed.x = final_vx;
        this.speed.y = final_vy;
        // console.log(`🏁 最终速度: 水平=${this.speed.x.toFixed(2)}, 垂直=${this.speed.y.toFixed(2)}`);
    }

    private onChange(x: number) {
        this.setSpeedByDis(x - this.heroNode.x)
    }

    private setControl(bol: boolean) {
        this.isControl = bol
        eventCenter.emit(EventType.PLAENT_CAN_CONTROL_JUMP, bol)
    }

    // 回到起跳点
    private reset() {
        this.setControl(true)
        this.isJump = false
        let pointIndex = this.getBirthPointIndex()
        if (this.isMain()) {
            pointIndex = Math.max(pointIndex, this.curIndex)
        }
        this.curIndex = pointIndex
        const pos = this.getFudaoCenter(pointIndex)
        if (this.isPointMoving(this.curIndex)) {
            const movingPoint = this.points[this.curIndex]
            const moveData: PointMoveData = movingPoint.Data
            if (moveData.moveType === PointMoveType.VERTICAL) {
                this.heroYOffsetOnMovingPoint = pos.y - movingPoint.y
                this.heroXOffsetOnMovingPoint = 0
            } else if (moveData.moveType === PointMoveType.HORIZONTAL) {
                this.heroXOffsetOnMovingPoint = pos.x - movingPoint.x
                this.heroYOffsetOnMovingPoint = 0
            }
        }
        this.heroNode.setPosition(pos.add(cc.v2(this.heroXOffsetOnMovingPoint, this.heroYOffsetOnMovingPoint)))
        this.lineIndex = 0
        this.heroNode.Component(HeroCmpt).playAnimation(PassengerLifeAnimation.IDLE, true)
    }

    private isMain() {
        return MAIN.includes(this.model.eventName)
    }

    private getBirthPointIndex() {
        let index = this.points.slice(0, this.curIndex + 1).findIndex(p => p.name.includes("birth"))
        if (index < 0) {
            return 0
        }
        return index
    }

    private updatePos(dt: number): { start: cc.Vec2, end: cc.Vec2 } {
        this.prePos = this.heroNode.getPosition(this.prePos)

        this.speed.y -= this.gravity * dt
        this.nextPos.x = this.prePos.x + this.speed.x * dt
        this.nextPos.y = this.prePos.y + this.speed.y * dt

        // console.log("⚡ 每帧位置更新:", dt);
        // console.log("从位置:", this.prePos.x.toFixed(1), this.prePos.y.toFixed(1));
        // console.log("到位置:", this.nextPos.x.toFixed(1), this.nextPos.y.toFixed(1));
        // console.log("当前速度:", this.speed.x.toFixed(1), this.speed.y.toFixed(1));

        // 构造轨迹
        const trajectory = {
            start: cc.v2(this.prePos.x, this.prePos.y),
            end: cc.v2(this.nextPos.x, this.nextPos.y)
        };

        // 更新角色位置
        this.heroNode.setPosition(this.nextPos)

        // 更新位置记录
        this.prePos.x = this.nextPos.x
        this.prePos.y = this.nextPos.y

        return trajectory;
    }

    private async checkClaimReward() {
        let model = this.model
        if (!model.checkRewardByPoint(this.curIndex)) return
        let rewardIndex = model.getRewardIndex(this.curIndex)
        let succ = await model.claimNodeReward(rewardIndex)
        if (succ) {
            this.flyReward(this.model.rewards[rewardIndex])
        }
        return succ
    }

    private async flyReward(reward) {
        let point = this.points[this.curIndex]
        let node = point.Child(this.rewardNode.name)
        ut.convertParent(node, this.planetCtrl["mapNode_"])
        node.addComponent(PlanetNodeRewardCmpt).init(reward)

        let items = [node]
        let go = (items: cc.Node[]) => {
            if (items.length <= 0) return
            let node = items[0]
            let cmpt = node.addComponent(PlanetNodeRewardGroupCmpt)
            return new Promise((r) => {
                cmpt.init(items, r)
                cmpt.fly()
            })
        }
        await Promise.all([go(items)])
    }

    private async showPoint(index) {
        let point = this.points[index]
        if (!point || point.active) return
        point.active = true
        if (!point.name.includes("_move")) {
            point.opacity = 0
            let orgY = point.y
            let offsetY = 10
            point.y -= 80
            await cc.tween(point).to(0.45, { opacity: 255, y: orgY + offsetY }, { easing: cc.easing.sineOut }).to(0.1, { y: orgY }, { easing: cc.easing.sineInOut }).promise()
        }
    }

    private checkIntersect(trajectory: { start: cc.Vec2, end: cc.Vec2 }) {
        this.initLines()
        let intersectPos = this.intersectPos
        for (let i = 0; i < this.points.length; i++) {
            if (!this.points[i].active) continue

            let lines = this.getPointLines(i)
            if (!lines) continue
            for (let j = 0; j < lines.length; j++) {
                if (i == this.curIndex && j != this.lineIndex) continue
                let { start, end } = lines[j]
                if (ut.lineLine(trajectory.start, trajectory.end, start, end, intersectPos)) {
                    if (!intersectPos.fuzzyEquals(trajectory.start, 0.01)) {
                        // 添加调试信息，特别关注当前平台的相交
                        if (i === this.curIndex) {
                            // console.log("🔴 检测到与当前平台相交!");
                            // console.log("角色轨迹:", trajectory.start.x, trajectory.start.y, "→", trajectory.end.x, trajectory.end.y);
                            // console.log("平台线段 start:", start.x, start.y);
                            // console.log("平台线段 end:", end.x, end.y);
                            // console.log("交点 intersectPos:", intersectPos.x, intersectPos.y);
                        }
                        return { index: i, lineIndex: j, pos: intersectPos }
                    }
                }
            }
        }
    }

    private handleIntersect(interactInfo) {
        // console.log("🔴 着陆了! 停留点索引:", interactInfo.index, "原来的索引:", this.curIndex);
        this.curIndex = interactInfo.index
        this.lineIndex = interactInfo.lineIndex
        this.isJump = false
        this.heroNode.setPosition(interactInfo.pos)

        // 如果落在移动停留点上，计算并保存hero相对于停留点的偏移
        if (this.isPointMoving(interactInfo.index)) {
            const movingPoint = this.points[interactInfo.index]
            const moveData: PointMoveData = movingPoint.Data

            if (moveData.moveType === PointMoveType.VERTICAL) {
                // 上下移动：保存Y偏移量
                this.heroYOffsetOnMovingPoint = interactInfo.pos.y - movingPoint.y
                this.heroXOffsetOnMovingPoint = 0
            } else if (moveData.moveType === PointMoveType.HORIZONTAL) {
                // 左右移动：保存X偏移量
                this.heroXOffsetOnMovingPoint = interactInfo.pos.x - movingPoint.x
                this.heroYOffsetOnMovingPoint = 0
            }
        }

        let cmpt = this.heroNode.Component(HeroCmpt)
        let p = cmpt.playAnimation(HeroAnimation.JUMP4).then(() => {
            this.heroNode.Component(HeroCmpt).playAnimation(PassengerLifeAnimation.IDLE, true)
        })
        let p2 = this.checkClaimReward()
        this.showPoint(interactInfo.index + 1)
        Promise.all([p, p2]).then(() => {
            this.model.setProgress(this.curIndex)
            if (this.curIndex < this.reachLines.length - 1) {
                this.setControl(true)
            }
        })
    }

    private clampDis(dis) {
        this.initLines()
        let eventName = this.model.eventName
        if (eventName == PlanetEvent.JUMP_1 || eventName == PlanetEvent.JUMP_2) return dis
        if (this.curIndex != this.reachLines.length - 2) return dis

        let lines = this.reachLines.last()
        let maxX = lines[0].start.x + 300
        let maxDis = maxX - this.prePos.x
        return Math.min(dis, maxDis)
    }

    private checkReachEnd(dis: number, x: number) {
        this.initLines()
        if (this.curIndex != this.reachLines.length - 2) return
        let lines = this.reachLines.last()
        let start = lines[0].start.x
        if (dis >= start - x) {
            eventCenter.emit(EventType.PLAENT_CAN_JUMP_TO_END)
        }
    }

    private checkDead() {
        if (this.heroNode.y <= this.minY) {
            return true
        }
    }

    // 刷新力度
    private updatePower() {
        if (this.touchTime === 0) {
            return
        }
        let passTime = (Date.now() - this.touchTime) * 0.001
        let per = passTime / MAX_TIME
        if (per > 1) per = 1
        this.powerSp.fillRange = per
    }

    // 力度显示
    private showPower(isShow: boolean) {
        this.powerSp.fillRange = 0
        this.powerSp.node.parent.active = isShow
    }

    update(dt: number) {
        super.update(dt)

        if (!this.model) return
        this.updatePower()
        this.actionTree && this.actionTree.update(dt)

        let isJump = this.isJump
        let tot = dt
        let stepDt = 1 / 60
        while (tot) {
            if (tot < stepDt) {
                stepDt = tot
            }
            tot -= stepDt
            this.fixedUpdate(stepDt)
        }

        if (isJump) {
            if (this.checkDead()) {
                this.reset()
                return
            }

            if (this.reachLines?.length && this.curIndex >= this.reachLines.length - 1) {
                let model = this.model
                this.enabled = false
                gameHelper.hero.setPosition(this.heroNode.getPosition())
                ut.wait(0.5, this).then(async () => {
                    this.planetCtrl.focusHero()
                    await model.die()
                    model.end()
                })
            }
        }
    }

    private fixedUpdate(dt) {
        if (this.isJump) {
            // 步骤1: 更新角色位置，获取轨迹
            const trajectory = this.updatePos(dt)

            // 步骤2: 轨迹与当前平台线段相交检测
            let interactInfo = this.checkIntersect(trajectory)
            if (interactInfo) {
                this.handleIntersect(interactInfo)
            }

            // 步骤3: 更新平台位置
            this.updateMovingPoints(dt)

            //下降的时候做双重判断
            if (this.speed.y <= 0) {
                if (!interactInfo) {
                    // 步骤4: 轨迹与更新后平台线段相交检测
                    interactInfo = this.checkIntersect(trajectory)
                    if (interactInfo) {
                        this.handleIntersect(interactInfo)
                    }
                }
            }

        } else {
            // 非跳跃状态正常更新平台
            this.updateMovingPoints(dt)
        }
    }

    protected onDeath(): void {
    }

    // ==================== 移动停留点相关方法 ====================

    // 初始化会移动的停留点的数据
    private initPointMoveData(point: cc.Node, moveType: PointMoveType) {
        const moveData: PointMoveData = {
            originalX: point.x,
            originalY: point.y,
            currentTime: 0,
            moveType: moveType,
            cycleDuration: 2.0
        }
        point.Data = moveData
    }

    // 更新所有移动停留点的位置
    private updateMovingPoints(dt: number) {
        this.movingPoints.forEach(pointIndex => {
            const point = this.points[pointIndex]
            if (point && point.active && point.Data) {
                this.updateSinglePointMove(point, dt)
            }
        })
    }

    // 更新会动的停留点 
    private updateSinglePointMove(point: cc.Node, dt: number) {
        const moveData: PointMoveData = point.Data
        moveData.currentTime += dt
        // 周期进度
        const cycleProgress = (moveData.currentTime % moveData.cycleDuration) / moveData.cycleDuration
        // 平滑移动
        const sinValue = Math.sin(cycleProgress * Math.PI * 2)
        switch (moveData.moveType) {
            case PointMoveType.VERTICAL:
                const newY = moveData.originalY + sinValue * MOVE_OFFSET
                point.y = newY
                break
            case PointMoveType.HORIZONTAL:
                const newX = moveData.originalX + sinValue * MOVE_OFFSET
                point.x = newX
                break
        }
        this.updateHeroOnMovingPoint(point)
    }

    // 更新hero在移动停留点上的位置
    private updateHeroOnMovingPoint(movingPoint: cc.Node) {
        if (this.isJump) return
        if (!this.isHeroOnThisMovingPoint(movingPoint)) return

        const moveData: PointMoveData = movingPoint.Data
        const currentHeroPos = this.heroNode.getPosition()
        switch (moveData.moveType) {
            case PointMoveType.VERTICAL:
                const newHeroY = movingPoint.y + this.heroYOffsetOnMovingPoint
                this.heroNode.setPosition(cc.v2(currentHeroPos.x, newHeroY))
                break
            case PointMoveType.HORIZONTAL:
                const newHeroX = movingPoint.x + this.heroXOffsetOnMovingPoint
                this.heroNode.setPosition(cc.v2(newHeroX, currentHeroPos.y))
                break
        }
        gameHelper.hero.setPosition(this.heroNode.getPosition())
    }

    // 判断hero是否在指定的移动停留点上
    private isHeroOnThisMovingPoint(movingPoint: cc.Node): boolean { return this.curIndex === this.points.indexOf(movingPoint) }

    // 获取平台在某一帧的瞬时速度
    private getPlatformInstantaneousVelocity(pointIndex: number): cc.Vec2 {
        if (!this.isPointMoving(pointIndex)) {
            return cc.v2(0, 0);
        }

        const point = this.points[pointIndex];
        const moveData: PointMoveData = point.Data;
        if (!moveData) {
            return cc.v2(0, 0);
        }

        const { currentTime, cycleDuration, moveType } = moveData;

        // 计算瞬时速度
        // 位置函数: position = originalPosition + sin(2π * t / T) * A
        // 速度函数: velocity = d(position)/dt = A * (2π / T) * cos(2π * t / T)
        const angularFrequency = (2 * Math.PI) / cycleDuration;  // ω = 2π / T
        const cosValue = Math.cos(angularFrequency * currentTime);  // cos(ωt)
        const speedValue = MOVE_OFFSET * angularFrequency * cosValue;  // A * ω * cos(ωt)

        if (moveType === PointMoveType.VERTICAL) {
            return cc.v2(0, speedValue);
        } else if (moveType === PointMoveType.HORIZONTAL) {
            return cc.v2(speedValue, 0);
        }

        return cc.v2(0, 0);
    }
}
