import EventType from "../../common/event/EventType";
import NodeType from "../../common/event/NodeType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import { Transport } from "../../model/transport/TransportModel";

const { ccclass } = cc._decorator;

@ccclass
export default class TransportPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected lvNode_: cc.Node = null // path://root/head/lv_n
    protected weightNode_: cc.Node = null // path://root/head/weight_n
    protected listSv_: cc.ScrollView = null // path://root/list_sv
    protected resetTimeLbl_: cc.Label = null // path://root/bottom/reset_time_l
    protected backNode_: cc.Node = null // path://back_be_n
    //@end

    private cb: Function = null

    public listenEventMaps() {
        return [
            { [EventType.DAILY_REFRESH]: this.initView },
            { [NodeType.GUIDE_TRANSPORT_WEIGHT]: () => this.weightNode_ }
        ]
    }

    public async onCreate() {
        await gameHelper.transport.checkSync()
    }

    public onEnter(cb) {
        this.initView()
    }

    public onRemove() {
        !!this.cb && this.cb()
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }

    // path://root/question_be
    onClickQuestion(event: cc.Event.EventTouch, data: string) {
    }

    // path://root/head/weight_n/explain_be
    onClickExplain(event: cc.Event.EventTouch, data: string) {
        let node = event.target
        viewHelper.showBubble("ExplainBubble", node, { desc: assetsMgr.lang("tip_transport_explain") }, { dir: cc.v2(0, -1) })
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private async takeTransport(index: number, onSuccess: Function) {
        let r = await gameHelper.transport.takeTransport(index)
        if (r) {
            onSuccess && onSuccess()
            this.updateLv()
            this.updateWeight()
        }
    }

    // ----------------------------------------- custom function ----------------------------------------------------


    update() {
    }

    private initView() {
        this.updateLv()
        this.updateWeight()

        let list = gameHelper.transport.getTransportList()

        this.listSv_.List(list.length, (it, i) => {
            let data = list[i]
            this.setOneItem(it, data, i)
        })
    }

    private updateLv() {
        const lv = gameHelper.transport.level

        this.lvNode_.Child("desc", cc.Label).setLocaleKey("ui_transport_text_2", lv)

        let progress = this.lvNode_.Child("progress")
        const { exp, need } = gameHelper.transport.getLevelUpInfo()
        this.updateProgress(progress, exp, need)
    }

    private updateWeight() {
        const total = gameHelper.train.getLoad()
        const current = gameHelper.transport.getLoad()
        let progress = this.weightNode_.Child("progress")
        this.updateProgress(progress, current, total)
    }

    private updateProgress(node: cc.Node, cur: number, max: number) {
        node.Child("mask").width = 342 * (cur / max)
        node.Child("lb", cc.Label).string = `${cur}/${max}`
    }

    private setOneItem(it: cc.Node, data: Transport, index: number) {
        let rewards = data.getRewards()

        it.Child("title/lbl").setLocaleKey("ui_transport_text_4", data.getStarLv())

        let state = data.getState()
        // 2和3共用一个
        if (state == proto.TransportDataState.Done) {
            state = proto.TransportDataState.Over
        }

        it.Child("state").Swih(state)
        if (state == proto.TransportDataState.NoneGet) {
            it.Child("state/0/desc/lbl").setLocaleKey("ui_transport_text_5", data.getLoad())
            const evt = it.Child("state/0/accept")
            evt.off('click')
            evt.on('click', () => {
                this.takeTransport(index, () => {
                    this.setOneItem(it, data, index)
                })
            })
        } else if (state == proto.TransportDataState.Pull) {
            const rightN = it.Child("state/1/right")
            cc.Tween.stopAllByTarget(rightN)
            rightN.x = 71.5
            cc.tween(rightN)
                .repeatForever(
                    cc.tween()
                        .delay(.8)
                        .to(.15, { x: 77.5 })
                        .to(.15, { x: 71.5 })
                        .to(.15, { x: 77.5 })
                        .to(.15, { x: 71.5 })
                )
                .start()
        }
        const tipN = it.Child("tip")
        const expN = tipN.Child("exp")
        expN.active = data.isRare()
        if (expN.active) {
            expN.Child("lbl", cc.Label).string = "+" + cfgHelper.getMiscData("transport").rareExp
        }
        tipN.Component(cc.MultiFrame).setFrame(data.isRare() || data.isTimeStoneKey())
        tipN.Child("desc").setLocaleKey(data.getLangKey())

        const iconN = it.Child("icon")
        iconN.Child("xing").active = data.isRare() || data.isTimeStoneKey()
        resHelper.loadRoleBigIcon(data.getActor(), iconN.Child("role_icon", cc.Sprite), this.getTag())


        it.Child('planetName', cc.Label).setLocaleKey(data.getName())
        it.Child('rewards').Items(rewards, (it, data) => {
            resHelper.loadIconByCondInfo(data, it.Child('icon'), this.getTag())
            it.Child('num', cc.Label).string = uiHelper.getShowNum(data)
            uiHelper.regClickPropBubble(it, data)
        })
    }


    lateUpdate() {
        this.resetTimeLbl_.setLocaleUpdate(() => {
            const time = ut.millisecondFormat(gameHelper.world.getNextDaySurpluTime(), `hh:mm:ss`)
            return assetsMgr.lang("ui_transport_text_6", time)
        })
    }

}
